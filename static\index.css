/* ======================================
   CSS Variables - Color Scheme & Global Settings
   ====================================== */
:root {
    /* Modern color palette */
    --primary-color: #1a3a5f;      /* Deep blue - Main brand color */
    --primary-light: #2c5282;      /* Lighter blue for hover states */
    --secondary-color: #4299e1;    /* Bright blue - Secondary brand color */
    --secondary-light: #63b3ed;    /* Lighter blue for hover effects */
    --accent-color: #38b2ac;       /* Teal - For CTAs and highlights */
    --accent-light: #4fd1c5;       /* Lighter teal for hover states */
    --sub-color: #805ad5;          /* Purple - For sub-options */
    --sub-light: #9f7aea;          /* Lighter purple for hover states */
    --background-color: #f7fafc;   /* Very light gray - Background */
    --card-bg: #ffffff;            /* White - Card backgrounds */
    --text-dark: #2d3748;          /* Dark gray - Primary text */
    --text-medium: #4a5568;        /* Medium gray - Secondary text */
    --text-light: #f7fafc;         /* Off-white - Text on dark backgrounds */
    --border-color: #e2e8f0;       /* Light gray - Borders */
    --error-color: #e53e3e;        /* Red - Error messages */
    --success-color: #38a169;      /* Green - Success messages */

    /* Effects and animations */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --radius-sm: 0.25rem;
    --radius: 0.5rem;
    --radius-lg: 1rem;
}

/* ======================================
   General Layout Styles
   ====================================== */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-dark);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Main container - Holds sidebar and main content */
.container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* Sidebar - Contains options and controls */
.sidebar {
    width: 320px; /* Increased from 280px */
    box-sizing: border-box;
    background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
    color: var(--text-light);
    padding: 15px 15px;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    z-index: 10;
}

/* Main content area */
.main_container {
    flex: 1;
    box-sizing: border-box;
    padding: 20px;
    padding-top: 0px;
    overflow-y: auto;
    background-color: var(--background-color);
}

/* App title styling */
.app-title {
    text-align: center;
    color: var(--primary-color);
    /* margin: 15px 0 30px; */
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 600;
    letter-spacing: -0.5px;
    position: relative;
    /* display: inline-block; */
    left: 50%;
    transform: translateX(-50%);
}

.app-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(to right, var(--accent-color), var(--secondary-color));
    border-radius: 2px;
}

/* ======================================
   Sidebar Components
   ====================================== */
.sidebar-header {
    text-align: center;
    padding: 0px 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
    font-family: 'Poppins', sans-serif;
    font-size: 22px;
    font-weight: 600;
    color: var(--text-light);
    margin: 0;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 10px;
}

.sidebar-header h2::before {
    content: '\f013'; /* Gear icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 10px;
    font-size: 18px;
    opacity: 0.9;
}

/* Navigation links in sidebar */
.nav-links {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
}

.nav-links li {
    margin-bottom: 8px;
}

.nav-links a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-light);
    padding: 12px 15px;
    border-radius: var(--radius);
    transition: var(--transition);
    font-weight: 500;
    background-color: rgba(255, 255, 255, 0.08);
}

.nav-links a:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateX(3px);
}

.nav-links i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
    font-size: 16px;
}

/* Prompt section in sidebar */
.prompt-section {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
}

.prompt-section h3 {
    margin: 0 0 12px 0;
    color: var(--text-light);
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.prompt-section h3::before {
    content: '\f075'; /* Comment icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 8px;
    font-size: 14px;
    opacity: 0.9;
}

/* Textarea for additional prompts */
.prompt-input {
    width: 100%;
    min-height: 150px;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--radius);
    background: rgba(255, 255, 255, 0.08);
    color: var(--text-light);
    resize: vertical;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    box-sizing: border-box;
    transition: var(--transition);
}

.prompt-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.3);
}

.prompt-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* Translation Settings Section */
.translation-settings {
    margin-top: 20px;
    padding: 15px;
    background-color: #315076;
    border-radius: var(--radius);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: var(--shadow-sm);
    flex: 1;
}

.translation-settings h3 {
    margin: 0 0 15px 0;
    color: var(--text-light);
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    padding-bottom: 8px;
}

.translation-settings h3::before {
    content: '\f1e0'; /* Share icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 8px;
    font-size: 14px;
    opacity: 0.9;
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    color: var(--text-light);
    font-size: 14px;
    font-weight: 500;
}

.settings-select, .settings-input {
    padding: 8px 10px;
    border-radius: var(--radius-sm);
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    font-size: 14px;
    transition: var(--transition);
}

/* Style for select dropdown options */
.settings-select option {
    background-color: #466284;
    color: white;
    padding: 8px;
}

.settings-select:focus, .settings-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.3);
}

/* Settings save button */
.settings-save-btn {
    margin-top: 15px;
    padding: 8px 12px;
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    color: var(--text-light);
    border: none;
    border-radius: var(--radius);
    font-size: 14px;
    font-weight: 600;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    position: relative;
    overflow: hidden;
}

/* Removed save icon */

.settings-save-btn:hover {
    background: linear-gradient(135deg, var(--secondary-light), var(--secondary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Override the button hover animation */
.settings-save-btn:hover::before {
    left: auto;
    transform: none;
}

.settings-save-btn:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-sm);
}

/* ======================================
   Main Content Styles
   ====================================== */
.file-upload-container {
    width: 75vw; /* Increased from 60vw for wider container */
    min-width: 320px;
    max-width: 1800px; /* Increased max-width from 1600px */
    margin: 1vh;
    background-color: var(--card-bg);
    padding: 2rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    position: relative;
    border: 1px solid var(--border-color);
    box-sizing: border-box; /* Ensure padding is included in width */
    overflow: hidden; /* Prevent overflow */
}

/* Two-column layout for desktop */
@media (min-width: 1200px) {
    .file-upload-container {
        padding: 3vh 3vw;
    }

    /* Main two-column layout container */
    .two-column-layout {
        display: flex;
        flex-wrap: nowrap;
        gap: 2vw;
        width: 100%;
        box-sizing: border-box;
        align-items: flex-start; /* Align items at the top */
    }

    /* Left column with file upload */
    .left-column {
        flex: 0 0 35%; /* Slightly reduced from 30% */
        width: 30%;
        box-sizing: border-box;
    }

    /* Right column with options */
    .right-column {
        flex: 0 0 65%; /* Fixed width of 70% */
        width: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding-right: 1vw; /* Add padding to prevent overflow */
        overflow: hidden; /* Prevent overflow */
    }

    /* Make the drop area taller in desktop view */
    #drop-area {
        height: 35vh;
        min-height: 250px;
        max-height: 350px;
        margin-top: 0; /* Ensure no top margin */
    }

    /* Position second page upload in the left column */
    #second-page-upload {
        margin-top: 20px;
    }

    /* Ensure sub-options appear properly */
    .sub-options {
        margin-top: 20px;
        width: 100%;
    }

    /* Make error message full width */
    #error-message {
        width: 100%;
        max-width: 600px;
        margin: 1.5rem auto 0;
    }

    /* Style the submit button */
    #submit-button {
        width: 100%;
        max-width: 400px;
        margin: 2rem auto 0;
    }

    /* Ensure the flip select is at the top of the form */
    .flip-select {
        margin-bottom: 25px;
    }
}

/* File drop areas */
#drop-area, #second-drop-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius);
    padding: 2.5rem 2rem;
    text-align: center;
    transition: var(--transition);
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(247, 250, 252, 0.8);
    cursor: default; /* Changed from pointer to default */
    position: relative;
    overflow: hidden;
    margin-top: 0; /* Ensure no top margin */
}

#drop-area p, #second-drop-area p {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    font-size: 1rem;
    color: var(--text-medium);
    font-weight: 500;
}

#drop-area p::before, #second-drop-area p::before {
    content: '\f093'; /* Upload icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 10px;
    font-size: 1.2rem;
    color: var(--secondary-color);
}

/* Hover and active states for drop areas */
#drop-area:hover,
#drop-area.highlight,
#second-drop-area:hover,
#second-drop-area.highlight {
    background-color: rgba(66, 153, 225, 0.05);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

#drop-area.highlight::after,
#second-drop-area.highlight::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(66, 153, 225, 0.1) 0%, rgba(247, 250, 252, 0) 70%);
    pointer-events: none;
}

/* File name display */
#file-name, #second-file-name {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: var(--text-medium);
    background-color: rgba(66, 153, 225, 0.05);
    padding: 8px 12px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
}

/* Preview area for uploaded files */
.preview-area {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.preview-area img {
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.preview-area img:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow);
}

/* File type icons */
.file-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--radius-sm);
    font-weight: bold;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.file-icon::before {
    content: '\f15b'; /* File icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2.5rem;
    opacity: 0.2;
}

.file-icon.PDF {
    background-color: #e53e3e; /* Red for PDF */
}

.file-icon.DOCX {
    background-color: #3182ce; /* Blue for DOCX */
}

/* ======================================
   Radio Button Groups
   ====================================== */
.radio-group {
    margin: 0 0 0px 0; /* Changed from 25px 0 to align top elements */
    width: 100%;
}

/* Document type selection group with background */
#document-type-group {
    background-color: #f5fafd;
    padding: 1rem 1rem; /* Further reduced padding */
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    box-sizing: border-box;
    width: 100%;
    margin-top: 0; /* Ensure no top margin */
    margin-bottom: 0; /* Ensure no bottom margin */
}

.radio-group h3 {
    margin: 0 0 12px 0; /* Reduced from 15px */
    color: var(--text-dark);
    text-align: center;
    font-weight: 600;
    font-size: 1rem; /* Reduced from 1.1rem */
    position: relative;
    padding-bottom: 8px; /* Reduced from 10px */
}

.radio-group h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px; /* Reduced from 40px */
    height: 2px; /* Reduced from 3px */
    background-color: var(--secondary-color);
    border-radius: 1px; /* Reduced from 1.5px */
}

/* Container for radio options */
.radio-options {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px; /* Reduced from 12px */
}

/* Individual radio option */
.radio-option {
    flex: 0 0 110px; /* Increased from 100px for wider cards */
    position: relative;
}

@media (min-width: 1200px) {
    .radio-option {
        flex: 0 0 calc(25% - 12px); /* Changed to 25% for 4 cards per row */
        max-width: 120px; /* Increased from 110px for wider cards */
    }
}

/* Hide the actual radio button */
.radio-option input[type="radio"] {
    display: none;
}

/* Custom radio button label styling */
.radio-option label {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--card-bg);
    padding: 6px 8px; /* Reduced padding */
    font-size: 13px; /* Increased from 12px */
    border-radius: var(--radius);
    transition: var(--transition);
    /* cursor: pointer; */
    color: var(--text-medium);
    text-align: center;
    white-space: normal; /* Allow text wrapping */
    overflow: hidden;
    line-height: 1.2; /* For better readability in two lines */
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    font-weight: 500;
    height: 44px; /* Increased from 42px to accommodate larger font */
}

/* Add icons to document type labels */
.radio-option label::before {
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 6px; /* Reduced from 8px */
    font-size: 13px; /* Increased to match text size */
    display: inline-block;
}

/* Specific icons for each document type */
#auto + label::before { content: '\f0e7'; } /* Lightning icon */
#id + label::before { content: '\f2bb'; } /* ID card icon */
#iqama + label::before { content: '\f2c2'; } /* ID badge icon */
#family-card + label::before { content: '\f0c0'; } /* Users icon */
#birth-certificate + label::before { content: '\f1ae'; } /* Child icon */
#tm + label::before { content: '\f0e7'; } /* Trademark icon */
#certification + label::before { content: '\f0a3'; } /* Certificate icon */
#file + label::before { content: '\f15b'; } /* File icon */

/* Selected radio button styling */
.radio-option input[type="radio"]:checked + label {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    border-color: transparent;
}

/* Make sure the icon is visible when label is selected */
.radio-option input[type="radio"]:checked + label::before {
    color: white; /* Ensure icon is white when background is colored */
}

/* Hover state for radio button labels */
.radio-option label:hover {
    border-color: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(66, 153, 225, 0.15);
}

/* Sub-options styling */
.sub-options {
    margin-top: 15px;
    background-color: #f5fafd;
    padding: 1rem 1rem; /* Reduced to match document type group */
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    box-sizing: border-box;
    width: 100%;
}

/* ======================================
   Button Styles
   ====================================== */
button {
    width: 100%;
    padding: 1rem;
    margin-top: 1.5rem;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
    color: var(--text-light);
    border: none;
    border-radius: var(--radius);
    font-size: 1rem;
    font-weight: 600;
    /* cursor: pointer; */
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
    font-family: 'Poppins', sans-serif;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

button:hover {
    background: linear-gradient(135deg, var(--accent-light), var(--accent-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

button:hover::before {
    left: 100%;
}

button:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-sm);
}

#submit-button {
    display: flex;
    align-items: center;
    justify-content: center;
}

#submit-button::after {
    content: '\f061'; /* Arrow right icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-left: 10px;
    font-size: 0.9rem;
    transition: var(--transition);
}

#submit-button:hover::after {
    transform: translateX(3px);
}

/* ======================================
   Image Rotation Controls
   ====================================== */
.flip-select {
    text-align: center;
    margin: 15px 0 25px;
    display: flex;
    justify-content: center;
    gap: 10px;
}

/* Hide the actual radio buttons */
.flip-select input[type="radio"] {
    display: none;
}

/* Custom styling for rotation option labels */
.flip-select label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-medium);
    background-color: var(--card-bg);
    box-shadow: var(--shadow-sm);
    font-size: 14px;
    font-weight: 500;
    min-width: 100px;
}

/* Add icons to rotation labels */
.flip-select label::before {
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 8px;
}

#flip-left + label::before { content: '\f0e2'; } /* Rotate left icon */
#no-flip + label::before { content: '\f057'; } /* Times circle icon */
#flip-right + label::before { content: '\f01e'; } /* Rotate right icon */

/* Hover state for rotation labels */
.flip-select label:hover {
    border-color: var(--secondary-color);
    background-color: rgba(66, 153, 225, 0.05);
    transform: translateY(-1px);
}

/* Selected state for rotation options */
.flip-select input[type="radio"]:checked + label {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    color: var(--text-light);
    border-color: transparent;
    box-shadow: var(--shadow);
}

/* ======================================
   Error Messages
   ====================================== */
.error-message {
    color: var(--error-color);
    margin-top: 15px;
    text-align: center;
    font-weight: 500;
    display: none; /* Hidden by default, shown via JavaScript */
    padding: 10px 15px;
    border-radius: var(--radius);
    background-color: rgba(229, 62, 62, 0.1);
    border: 1px solid rgba(229, 62, 62, 0.2);
    font-size: 14px;
}

.error-message::before {
    content: '\f071'; /* Warning icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 8px;
}

/* ======================================
   Sub-option Styling
   ====================================== */
/* Different styling for sub-option radio buttons */
#id-types .radio-option label,
#iqama-types .radio-option label,
#family-card-types .radio-option label,
#tm-types .radio-option label {
    background-color: rgba(128, 90, 213, 0.05);
    border-color: rgba(128, 90, 213, 0.2);
    color: var(--sub-color);
}

/* Add icons to sub-option labels */
#id-types .radio-option label::before,
#iqama-types .radio-option label::before,
#family-card-types .radio-option label::before,
#tm-types .radio-option label::before {
    color: var(--sub-color);
}

/* Specific icons for sub-options */
#new-id + label::before { content: '\f2bb'; } /* ID card icon */
#normal-id + label::before { content: '\f2bb'; } /* ID card icon */
#old-id + label::before { content: '\f2bb'; } /* ID card icon */

#absher-iqama + label::before { content: '\f2c2'; } /* ID badge icon */
#normal-iqama + label::before { content: '\f2c2'; } /* ID badge icon */
#dependent-absher-iqama + label::before { content: '\f2c2'; } /* ID badge icon */
#dependent-normal-iqama + label::before { content: '\f2c2'; } /* ID badge icon */

#one-page + label::before { content: '\f0c0'; } /* Users icon */
#two-pages + label::before { content: '\f0c0'; } /* Users icon */
#one-page-new + label::before { content: '\f0c0'; } /* Users icon */
#two-pages-new + label::before { content: '\f0c0'; } /* Users icon */

#full_tm + label::before { content: '\f0e7'; } /* Trademark icon */
#meaning_tm + label::before { content: '\f0e7'; } /* Trademark icon */
#ashraf_tm + label::before { content: '\f0e7'; } /* Trademark icon */
#yasser_tm + label::before { content: '\f0e7'; } /* Trademark icon */

/* Selected state for sub-options */
#id-types .radio-option input[type="radio"]:checked + label,
#iqama-types .radio-option input[type="radio"]:checked + label,
#family-card-types .radio-option input[type="radio"]:checked + label,
#tm-types .radio-option input[type="radio"]:checked + label {
    background: linear-gradient(135deg, var(--sub-color), var(--sub-light));
    color: white;
    border-color: transparent;
}

/* Make sure the icon is visible when sub-option label is selected */
#id-types .radio-option input[type="radio"]:checked + label::before,
#iqama-types .radio-option input[type="radio"]:checked + label::before,
#family-card-types .radio-option input[type="radio"]:checked + label::before,
#tm-types .radio-option input[type="radio"]:checked + label::before {
    color: white; /* Ensure icon is white when background is colored */
}

/* Hover state for sub-options */
#id-types .radio-option label:hover,
#iqama-types .radio-option label:hover,
#family-card-types .radio-option label:hover,
#tm-types .radio-option label:hover {
    border-color: var(--sub-color);
    background-color: rgba(128, 90, 213, 0.1);
}
/* ======================================
   Download Button
   ====================================== */
.download-button {
    background: linear-gradient(135deg, var(--success-color), #2f855a);
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    margin: 15px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    max-width: 250px;
}

.download-button::before {
    content: '\f019'; /* Download icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 10px;
    display: inline-block;
    transition: transform 0.3s ease;
}

.download-button:hover {
    background: linear-gradient(135deg, #2f855a, var(--success-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.download-button:hover::before {
    transform: translateY(1px);
}

.download-button:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-sm);
}

/* ======================================
   Results Sections
   ====================================== */
.results-section {
    margin: 30px auto;
    padding: 20px;
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    max-width: 90%;
    overflow-x: auto; /* For horizontal scrolling on small screens */
    border: 1px solid var(--border-color);
    position: relative;
}

/* Section header with title and copy button */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.results-section h2 {
    color: var(--text-dark);
    margin: 0;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 1.3rem;
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
    border-radius: 1.5px;
}

/* Copy button styling */
.copy-btn {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    color: white;
    border: none;
    border-radius: var(--radius);
    padding: 6px 12px;
    font-size: 0.85rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    margin: 0;
    box-shadow: var(--shadow-sm);
    min-width: 80px;
    max-width: 80px;
    height: 32px;
}

.copy-btn:hover {
    background: linear-gradient(135deg, var(--secondary-light), var(--secondary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.copy-btn:active {
    transform: translateY(1px);
}

.copy-btn i {
    font-size: 0.85rem;
    margin-right: 5px;
}

/* Styling for data items in results */
.data-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.data-item {
    padding: 12px 15px;
    background-color: rgba(66, 153, 225, 0.05);
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.data-item:hover {
    background-color: rgba(66, 153, 225, 0.1);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.data-key {
    font-weight: 600;
    color: var(--primary-color);
    display: block;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.data-value {
    color: var(--text-medium);
    word-break: break-word;
}

/* Styling for original lines display */
.original-lines {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    padding: 15px;
    border-radius: var(--radius);
    background-color: rgba(247, 250, 252, 0.8);
    margin-top: 15px;
}

.line-item {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-medium);
}

.line-item:last-child {
    border-bottom: none;
}

/* JSON view container */
.json-view-container {
    max-height: 400px;
    overflow: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    padding: 15px;
    background-color: rgba(247, 250, 252, 0.8);
    margin-top: 15px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
}

/* AI response and meaning text */
.ai-response, .meaning-text {
    background-color: rgba(247, 250, 252, 0.8);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    padding: 15px;
    white-space: pre-wrap;
    font-family: 'Inter', sans-serif;
    color: var(--text-medium);
    line-height: 1.6;
    max-height: 400px;
    overflow-y: auto;
}

/* ======================================
   Accessibility & Screen Reader
   ====================================== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ======================================
   Notification System
   ====================================== */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    width: 350px;
    max-width: 90%;
}

.notification {
    background-color: white;
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    padding: 15px;
    margin-bottom: 15px;
    transform: translateX(120%);
    transition: transform 0.5s ease;
    display: flex;
    align-items: flex-start;
    overflow: hidden;
    border-left: 4px solid var(--secondary-color);
}

.notification.show {
    transform: translateX(0);
}

.notification-icon {
    margin-right: 15px;
    font-size: 20px;
    color: var(--secondary-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-dark);
    font-size: 16px;
}

.notification-message {
    color: var(--text-medium);
    font-size: 14px;
    line-height: 1.4;
}

.notification-close {
    color: var(--text-medium);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    margin-left: 10px;
    opacity: 0.5;
    transition: opacity 0.3s ease;
    width: auto;
    height: auto;
    box-shadow: none;
}

.notification-close:hover {
    opacity: 1;
    background: none;
    transform: none;
    box-shadow: none;
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    width: 100%;
    background-color: rgba(66, 153, 225, 0.2);
}

.notification-progress-bar {
    height: 100%;
    background-color: var(--secondary-color);
    width: 100%;
    animation: progress 5s linear forwards;
}

@keyframes progress {
    from { width: 100%; }
    to { width: 0%; }
}

/* Success notification */
.notification.success {
    border-left-color: var(--success-color);
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.success .notification-progress-bar {
    background-color: var(--success-color);
}

/* Error notification */
.notification.error {
    border-left-color: var(--error-color);
}

.notification.error .notification-icon {
    color: var(--error-color);
}

.notification.error .notification-progress-bar {
    background-color: var(--error-color);
}

/* ======================================
   Responsive Design
   ====================================== */
/* Medium screens - Tablet */
@media (min-width: 769px) and (max-width: 1199px) {
    .file-upload-container {
        padding: 2.5vh 3vw;
        width: 65vw; /* Increased from 50vw to match the wider desktop container */
    }

    /* Stack columns on tablet */
    .two-column-layout {
        display: flex;
        flex-direction: column;
    }

    .left-column,
    .right-column {
        width: 100%;
        max-width: 100%;
    }

    .right-column {
        margin-top: 2vh; /* Reduced from 4vh for better alignment */
    }

    #drop-area {
        height: 30vh;
        min-height: 200px;
    }

    .radio-option {
        flex: 0 0 calc(25% - 10px); /* Changed to 25% for 4 cards per row */
        min-width: 100px; /* Reduced from 120px */
    }

    /* Adjust document type group padding */
    #document-type-group,
    .sub-options {
        padding: 1.5vh 2vw;
    }
}

/* Small screens - Mobile */
@media (max-width: 768px) {
    /* Stack columns on mobile */
    .two-column-layout {
        display: flex;
        flex-direction: column;
    }

    .left-column,
    .right-column {
        width: 100%;
        max-width: 100%;
    }

    .right-column {
        margin-top: 2vh; /* Reduced from 3vh for better alignment */
    }

    .file-upload-container {
        padding: 2vh 4vw;
        width: 65vw; /* Increased from 50vw to match the wider desktop container */
    }

    #drop-area {
        height: 25vh;
        min-height: 150px;
    }

    /* Adjust document type group padding */
    #document-type-group,
    .sub-options {
        padding: 1.5vh 3vw;
    }

    .radio-option {
        flex: 0 0 calc(50% - 8px);
        min-width: 90px; /* Reduced from 100px */
    }
    .container {
        flex-direction: column;
        height: auto;
        overflow: visible;
    }

    .sidebar {
        width: 100%;
        height: auto;
        min-height: 200px;
        border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    }

    .main_container {
        width: 100%;
        padding: 15px;
    }

    .file-upload-container {
        width: 100%;
        padding: 1.5rem;
        margin-top: 30px;


    }

    .radio-option {
        flex: 0 0 130px;
    }

    .app-title {
        font-size: 2rem;
        margin: 20px 0;
    }

    .radio-options {
        gap: 10px;
    }

    .radio-option label {
        padding: 10px 12px;
        font-size: 13px;
        height: 40px;
    }

    .data-list {
        grid-template-columns: 1fr;
    }

    .flip-select {
        flex-wrap: wrap;
    }
}

/* Additional styles for very small screens (phones) */
@media (max-width: 480px) {
    body {
        font-size: 15px;
    }

    .app-title {
        font-size: 1.8rem;
        margin: 15px 0 25px;
    }

    .app-title::after {
        width: 40px;
        height: 3px;
    }

    .file-upload-container {
        padding: 1.25rem;
        border-radius: var(--radius);
    }

    #drop-area, #second-drop-area {
        padding: 1.5rem 1rem;
        height: 150px;
    }

    #drop-area p, #second-drop-area p {
        font-size: 0.9rem;
    }

    .radio-option {
        flex: 0 0 100px;
    }

    .radio-option label {
        padding: 8px 10px;
        font-size: 12px;
        height: 38px;
    }

    .flip-select {
        margin: 10px 0 20px;
    }

    .flip-select label {
        padding: 6px 10px;
        font-size: 13px;
        min-width: 80px;
    }

    button {
        padding: 0.8rem;
        font-size: 0.95rem;
    }

    .prompt-section {
        height: auto;
        min-height: 120px;
    }

    /* Results section adjustments for phones */
    .results-section {
        padding: 15px;
        max-width: 95%;
        margin: 20px auto;
        border-radius: var(--radius);
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .results-section h2 {
        font-size: 1.2rem;
        margin-bottom: 5px;
    }

    .section-header::after {
        width: 40px;
        height: 2px;
    }

    .copy-btn {
        align-self: flex-end;
        font-size: 0.8rem;
        padding: 5px 10px;
    }

    .json-view-container,
    .original-lines,
    .ai-response,
    .meaning-text {
        max-height: 250px;
        padding: 12px;
        font-size: 13px;
    }

    /* Improve form elements spacing */
    .radio-group {
        margin: 10px 0;
    }

    .radio-group h3 {
        font-size: 1rem;
    }

    .sub-options {
        padding: 12px;
    }

    .file-icon {
        width: 70px;
        height: 70px;
        font-size: 0.9rem;
    }

    #file-name, #second-file-name {
        font-size: 0.8rem;
        padding: 6px 10px;
    }

    .download-button {
        padding: 10px 15px;
        font-size: 0.9rem;
        max-width: 200px;
    }
}